
JeecgBoot低代码平台(商业版介绍)
===============



项目介绍
-----------------------------------

<h3 align="center">企业级AI低代码平台</h3>


JeecgBoot是一款集成AI应用的，基于BPM流程的低代码平台，旨在帮助企业快速实现低代码开发和构建个性化AI应用！前后端分离架构Ant Design&Vue3，SpringBoot，SpringCloud Alibaba，Mybatis-plus，Shiro。强大的代码生成器让前后端代码一键生成，无需写任何代码！ 引领AI低代码开发模式: AI生成->OnlineCoding-> 代码生成-> 手工MERGE， 帮助Java项目解决80%的重复工作，让开发更多关注业务，提高效率、节省成本，同时又不失灵活性！低代码能力：Online表单、表单设计、流程设计、Online报表、大屏/仪表盘设计、报表设计; AI应用平台功能：AI知识库问答、AI模型管理、AI流程编排、AI聊天等，支持含ChatGPT、DeepSeek、Ollama等多种AI大模型

JeecgBoot 提供了一系列 `低代码能力`，实现`真正的零代码`在线开发：Online表单开发、Online报表、复杂报表设计、打印设计、在线图表设计、仪表盘设计、大屏设计、移动图表能力、表单设计器、在线设计流程、流程自动化配置、插件能力（可插拔）

`AI赋能低代码:` 目前提供了AI应用、AI模型管理、AI流程编排、AI对话助手，AI建表、AI写文章、AI知识库问答、AI字段建议等功能;支持各种AI大模型ChatGPT、DeepSeek、Ollama、智普、千问等.

`JEECG宗旨是:` 简单功能由OnlineCoding配置实现，做到`零代码开发`；复杂功能由代码生成器生成进行手工Merge 实现`低代码开发`，既保证了`智能`又兼顾`灵活`；实现了低代码开发的同时又支持灵活编码，解决了当前低代码产品普遍不灵活的弊端！

`JEECG业务流程:` 采用工作流来实现、扩展出任务接口，供开发编写业务逻辑，表单提供多种解决方案： 表单设计器、online配置表单、编码表单。同时实现了流程与表单的分离设计（松耦合）、并支持任务节点灵活配置，既保证了公司流程的保密性，又减少了开发人员的工作量。



#### JeecgBoot商业版与同类产品区别
-----------------------------------

- 灵活性：jeecgboot基于开源技术栈，设计初考虑到可插拔性和集成灵活性，确保平台的智能性与灵活性，避免因平台过于庞大而导致的扩展困难。
- 流程管理：支持一个表单挂接多个流程，同时一个流程可以连接多个表单，增强了流程的灵活性和复杂性管理。
- 符合中国国情的流程：针对中国市场的特定需求，jeecgboot能够实现各种符合中国国情的业务流程。
- 强大的表单设计器：jeecgboot的表单设计器与敲敲云共享，具备高质量和智能化的特点，能够满足零代码应用的需求，业内同类产品中不多见。
- 报表功能：自主研发的报表工具，拥有独立知识产权，功能上比业内老牌产品如帆软更智能，操作简便。
- BI产品整合：提供大屏、仪表盘、门户等功能，完美解决这些需求，并支持移动面板的设计与渲染。
- 自主研发的模块：jeecgboot的所有模块均为自主研发，具有独立的知识产权。
- 颗粒度和功能细致：在功能细致度和颗粒度上，jeecgboot远超同类产品，尤其在零代码能力方面表现突出。
- 零代码应用管理：最新版支持与敲敲云的零代码应用管理能力的集成，使得jeecgboot既具备低代码，又具备零代码的应用能力，业内独一无二。
- 强大的代码生成器：作为开源代码生成器的先锋，jeecgboot在代码生成的智能化和在线低代码与代码生成的结合方面，优势明显。
- 精细化权限管理：提供行级和列级的数据权限控制，满足企业在ERP和OA领域对权限管理的严格需求。
- 多平台支持的APP：目前采用uniapp3实现，支持小程序、H5、App及鸿蒙、鸿蒙Next、Electron桌面应用等多种终端。

> 综上所述，jeecgboot不仅在功能上具备丰富性和灵活性，还在技术架构、权限管理和用户体验等方面展现出明显的优势，是一个综合性能强大的低代码平台。



商业版演示
-----------------------------------

JeecgBoot vs 敲敲云
> - JeecgBoot是低代码产品拥有系列低代码能力，比如流程设计、表单设计、大屏设计，代码生成器，适合半开发模式（开发+低代码结合），也可以集成零代码应用管理模块.
> - 敲敲云是零代码产品，完全不写代码，通过配置搭建业务系统，其在jeecgboot基础上研发而成，删除了online、代码生成、OA等需要编码功能，只保留应用管理功能和聊天、日程、文件三个OA组件.


- JeecgBoot低代码：  https://boot3.jeecg.com
- 敲敲云零代码：https://app.qiaoqiaoyun.com
- APP演示(多端): http://jeecg.com/appIndex


### 流程视频介绍

[![](https://jeecgos.oss-cn-beijing.aliyuncs.com/files/flow_video.png)](https://www.bilibili.com/video/BV1Nk4y1o7Qc)



### 商业版功能简述

> 详细的功能介绍，[请联系官方](https://jeecg.com/vip)

```
│─更多商业功能
│  ├─流程设计器
│  ├─简流设计器(类钉钉版)
│  ├─门户设计（NEW）
│  ├─表单设计器
│  ├─大屏设计器
│  └─我的任务
│  └─历史流程
│  └─历史流程
│  └─流程实例管理
│  └─流程监听管理
│  └─流程表达式
│  └─我发起的流程
│  └─我的抄送
│  └─流程委派、抄送、跳转
│  └─OA办公组件
│  └─零代码应用管理（无需编码，在线搭建应用系统）
│  ├─积木报表企业版（含jimureport、jimubi）
│  ├─AI流程设计器源码
│  ├─Online全模块功能和源码
│  ├─AI写文章（CMS）
│  ├─AI表单字段建议（表单设计器）
│  ├─OA办公协同组件
│  ├─在线聊天功能
│  ├─设计表单移动适配
│  ├─设计表单支持外部填报
│  ├─设计表单AI字段建议
│  ├─设计表单视图功能(支持多种类型含日历、表格、看板、甘特图)
│  └─。。。
   
```




##### 流程设计
![](https://oscimg.oschina.net/oscnet/up-981ce174e4fbb48c8a2ce4ccfd7372e2994.png)

![](https://oscimg.oschina.net/oscnet/up-1dc0d052149ec675f3e4fad632b82b48add.png)

![](https://oscimg.oschina.net/oscnet/up-de31bc2f9d9b8332c554b0954cc73d79593.png)

![输入图片说明](https://static.oschina.net/uploads/img/201907/05165142_yyQ7.png "在这里输入图片标题")

![输入图片说明](https://static.oschina.net/uploads/img/201904/14160917_9Ftz.png "在这里输入图片标题")

![输入图片说明](https://static.oschina.net/uploads/img/201904/14160633_u59G.png "在这里输入图片标题")



##### 表单设计器
![](https://oscimg.oschina.net/oscnet/up-5f8cb657615714b02190b355e59f60c5937.png)

![](https://oscimg.oschina.net/oscnet/up-d9659b2f324e33218476ec98c9b400e6508.png)

![](https://oscimg.oschina.net/oscnet/up-4868615395272d3206dbb960ade02dbc291.png)
