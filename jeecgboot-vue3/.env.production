# 是否启用mock
VITE_USE_MOCK = false

# 发布路径
VITE_PUBLIC_PATH = /

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

#后台接口父地址(必填)
VITE_GLOB_API_URL=/jeecgboot

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://jeecg-boot-system:8080/jeecg-boot

# 接口父路径前缀
VITE_GLOB_API_URL_PREFIX=


# 填写后将作为乾坤子应用启动，主应用注册时AppName需保持一致（放开 VITE_GLOB_QIANKUN_MICRO_APP_NAME 参数表示jeecg-vue3将以乾坤子应用模式启动）
#VITE_GLOB_QIANKUN_MICRO_APP_NAME=jeecg-vue3
# 作为乾坤子应用启动时必填，需与qiankun主应用注册子应用时填写的 entry 保持一致
#VITE_GLOB_QIANKUN_MICRO_APP_ENTRY=//qiankun.boot3.jeecg.com/jeecg-vue3

# 全局隐藏哪些布局。可选属性：sider,header,multi-tabs；多个用逗号隔开
#VITE_GLOB_HIDE_LAYOUT_TYPES=sider,header,multi-tabs

# 在线文档编辑版本。可选属性：wps, offlineWps(离线版), onlyoffice
VITE_GLOB_ONLINE_DOCUMENT_VERSION=wps
