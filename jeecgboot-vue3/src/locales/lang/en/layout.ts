export default {
  footer: { onlinePreview: 'Preview', onlineDocument: 'Document' },
  header: {
    // user dropdown
    dropdownItemDoc: 'Document',
    dropdownItemLoginOut: 'Login Out',
    dropdownItemSwitchPassword: 'Password Change',
    dropdownItemSwitchDepart: 'Switch Department',
    dropdownItemRefreshCache: 'Clean cache',
    dropdownItemSwitchAccount: 'Account Setting',

    tooltipErrorLog: 'Error log',
    tooltipLock: 'Lock screen',
    tooltipNotify: 'Notification',

    tooltipEntryFull: 'Full Screen',
    tooltipExitFull: 'Exit Full Screen',

    // lock
    lockScreenPassword: 'Password',
    lockScreen: 'Lock screen',
    lockScreenBtn: 'Locking',

    home: 'Home',
    welcomeIn: 'Welcome in',
    refreshCacheComplete: 'Refresh cache complete',
    refreshCacheFailure: 'Refresh cache failure',
  },
  multipleTab: {
    reload: 'Refresh current',
    close: 'Close current',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeOther: 'Close Other',
    closeAll: 'Close All',
    homeDesign: 'Home Design',
  },
  setting: {
    // content mode
    contentModeFull: 'Full',
    contentModeFixed: 'Fixed width',
    // topMenu align
    topMenuAlignLeft: 'Left',
    topMenuAlignRight: 'Center',
    topMenuAlignCenter: 'Right',
    // menu trigger
    menuTriggerNone: 'Not Show',
    menuTriggerBottom: 'Bottom',
    menuTriggerTop: 'Top',
    // menu type
    menuTypeSidebar: 'Left menu mode',
    menuTypeMixSidebar: 'Left menu mixed mode',
    menuTypeMix: 'Top Menu Mix mode',
    menuTypeTopMenu: 'Top menu mode',

    on: 'On',
    off: 'Off',
    minute: 'Minute',

    operatingTitle: 'Successful!',
    operatingContent: 'The copy is successful, please go to src/settings/projectSetting.ts to modify the configuration!',
    resetSuccess: 'Successfully reset!',

    copyBtn: 'Copy',
    clearBtn: 'Clear cache and to the login page',

    drawerTitle: 'Configuration',

    darkMode: 'Dark mode',
    navMode: 'Navigation mode',
    interfaceFunction: 'Interface function',
    interfaceDisplay: 'Interface display',
    animation: 'Animation',
    splitMenu: 'Split menu',
    closeMixSidebarOnChange: 'Switch page to close menu',

    sysTheme: 'System theme',
    headerTheme: 'Header theme',
    sidebarTheme: 'Menu theme',

    menuDrag: 'Drag Sidebar',
    menuSearch: 'Menu search',
    menuAccordion: 'Sidebar accordion',
    menuCollapse: 'Collapse menu',
    collapseMenuDisplayName: 'Collapse menu display name',
    topMenuLayout: 'Top menu layout',
    menuCollapseButton: 'Menu collapse button',
    contentMode: 'Content area width',
    expandedMenuWidth: 'Expanded menu width',

    breadcrumb: 'Breadcrumbs',
    breadcrumbIcon: 'Breadcrumbs Icon',
    tabs: 'Tabs',
    tabDetail: 'Tab Detail',
    tabsQuickBtn: 'Tabs quick button',
    tabsRedoBtn: 'Tabs redo button',
    tabsFoldBtn: 'Tabs flod button',
    tabsTheme: 'tabs theme',
    tabsThemeSmooth: 'Smooth',
    tabsThemeCard: 'Card',
    tabsThemeSimple: 'Simple',
    sidebar: 'Sidebar',
    header: 'Header',
    footer: 'Footer',
    fullContent: 'Full content',
    grayMode: 'Gray mode',
    colorWeak: 'Color Weak Mode',

    progress: 'Progress',
    switchLoading: 'Switch Loading',
    switchAnimation: 'Switch animation',
    animationType: 'Animation type',

    autoScreenLock: 'Auto screen lock',
    notAutoScreenLock: 'Not auto lock',

    fixedHeader: 'Fixed header',
    fixedSideBar: 'Fixed Sidebar',

    mixSidebarTrigger: 'Mixed menu Trigger',
    triggerHover: 'Hover',
    triggerClick: 'Click',

    mixSidebarFixed: 'Fixed expanded menu',
  },
  changePassword: {
    changePassword: 'Change password',
    oldPassword: 'Old password',
    newPassword: 'New password',
    confirmNewPassword: 'Confirm new password',
    pleaseEnterNewPassword: 'Please enter new password',
  },
};
